<!DOCTYPE html>
<html>
<head>
    <title>CIS Controls v8 Audit Report - Windows Server 2019</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { background-color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .summary-item { text-align: center; padding: 15px; border-radius: 5px; }
        .compliant { background-color: #d4edda; color: #155724; }
        .non-compliant { background-color: #f8d7da; color: #721c24; }
        .not-configured { background-color: #fff3cd; color: #856404; }
        .error { background-color: #f5c6cb; color: #721c24; }
        table { width: 100%; border-collapse: collapse; background-color: white; border-radius: 5px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #34495e; color: white; }
        tr:hover { background-color: #f5f5f5; }
        .status-compliant { color: #28a745; font-weight: bold; }
        .status-non-compliant { color: #dc3545; font-weight: bold; }
        .status-not-configured { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .risk-high { background-color: #dc3545; color: white; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; }
        .risk-medium { background-color: #ffc107; color: black; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; }
        .risk-low { background-color: #28a745; color: white; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; }
        .remediation { font-family: monospace; background-color: #f8f9fa; padding: 5px; border-radius: 3px; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>CIS Controls v8 Audit Report</h1>
        <h2>Windows Server 2019 Environment</h2>
        <p>Generated: 2025-08-19 13:41:52</p>
        <p>Total Systems Audited: 1</p>
    </div>
    
    <div class="summary">
        <h2>Compliance Summary</h2>
        <div class="summary-grid">
            <div class="summary-item compliant">
                <h3>4</h3>
                <p>Compliant Controls</p>
            </div>
            <div class="summary-item non-compliant">
                <h3>5</h3>
                <p>Non-Compliant Controls</p>
            </div>
            <div class="summary-item not-configured">
                <h3>11</h3>
                <p>Not Configured</p>
            </div>
            <div class="summary-item error">
                <h3>0</h3>
                <p>Errors</p>
            </div>
        </div>
        <div style="margin-top: 20px; text-align: center;">
            <h3>Overall Compliance: 20%</h3>
            <p><strong>High Risk Issues:</strong> 1 | <strong>Medium Risk Issues:</strong> 4</p>
        </div>
    </div>
    
    <div style="background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2>Detailed Audit Results</h2>
        <table>
            <thead>
                <tr>
                    <th>Computer</th>
                    <th>Control ID</th>
                    <th>Control Name</th>
                    <th>Current Value</th>
                    <th>Recommended</th>
                    <th>Status</th>
                    <th>Risk Level</th>
                    <th>Remediation</th>
                </tr>
            </thead>
            <tbody>                <tr>
                    <td>PRD</td>
                    <td>1.2</td>
                    <td>Asset Inventory - SSDP Discovery</td>
                    <td>Manual</td>
                    <td>4</td>
                    <td class="status-non-compliant">Non-Compliant</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td><div class='remediation'>Set-Service -Name 'SSDPSRV' -StartupType Disabled</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>12.1</td>
                    <td>Network Infrastructure - Windows Firewall Domain</td>
                    <td>Not Set</td>
                    <td>1</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\DomainProfile' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\DomainProfile' -Name 'EnableFirewall' -Value 1</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>5.2</td>
                    <td>Account Management - Account Lockout</td>
                    <td>10</td>
                    <td>5</td>
                    <td class="status-non-compliant">Non-Compliant</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td><div class='remediation'>Configure via Group Policy: Computer Configuration > Policies > Windows Settings > Security Settings > Account Policies > Account Lockout Policy</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>10.2</td>
                    <td>Malware Defenses - Real-time Protection</td>
                    <td>Not Set</td>
                    <td>0</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection' -Name 'DisableRealtimeMonitoring' -Value 0</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>9.1</td>
                    <td>Email/Web Protection - SmartScreen</td>
                    <td>Not Set</td>
                    <td>1</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\System' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\System' -Name 'EnableSmartScreen' -Value 1</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>4.2</td>
                    <td>Secure Configuration - Anonymous SID Enumeration</td>
                    <td>1</td>
                    <td>1</td>
                    <td class="status-compliant">Compliant</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td>N/A</td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>1.1</td>
                    <td>Asset Inventory - Network Discovery</td>
                    <td>Manual</td>
                    <td>4</td>
                    <td class="status-non-compliant">Non-Compliant</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td><div class='remediation'>Set-Service -Name 'FDResPub' -StartupType Disabled</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>16.1</td>
                    <td>Application Security - PowerShell Execution Policy</td>
                    <td>Restricted</td>
                    <td>RemoteSigned</td>
                    <td class="status-non-compliant">Non-Compliant</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td><div class='remediation'>Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\PowerShell\1\ShellIds\Microsoft.PowerShell' -Name 'ExecutionPolicy' -Value RemoteSigned</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>10.1</td>
                    <td>Malware Defenses - Windows Defender</td>
                    <td>Not Set</td>
                    <td>0</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender' -Name 'DisableAntiSpyware' -Value 0</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>5.1</td>
                    <td>Account Management - Password Policy</td>
                    <td>0</td>
                    <td>14</td>
                    <td class="status-non-compliant">Non-Compliant</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>Configure via Group Policy: Computer Configuration > Policies > Windows Settings > Security Settings > Account Policies > Password Policy</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>7.1</td>
                    <td>Vulnerability Management - Windows Update</td>
                    <td>Not Set</td>
                    <td>0</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU' -Name 'NoAutoUpdate' -Value 0</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>8.1</td>
                    <td>Audit Log Management - Success Logon</td>
                    <td>Success and Failure</td>
                    <td>Success</td>
                    <td class="status-compliant">Compliant</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td>N/A</td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>8.2</td>
                    <td>Audit Log Management - Failure Logon</td>
                    <td>Success and Failure</td>
                    <td>Failure</td>
                    <td class="status-compliant">Compliant</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td>N/A</td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>2.1</td>
                    <td>Software Inventory - Windows Installer</td>
                    <td>Not Set</td>
                    <td>0</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\Installer' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\Installer' -Name 'AlwaysInstallElevated' -Value 0</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>6.1</td>
                    <td>Access Control - User Rights Assignment</td>
                    <td>Not Set</td>
                    <td>Administrators Authenticated Users</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td>N/A</td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>12.2</td>
                    <td>Network Infrastructure - Windows Firewall Private</td>
                    <td>Not Set</td>
                    <td>1</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PrivateProfile' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PrivateProfile' -Name 'EnableFirewall' -Value 1</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>11.1</td>
                    <td>Data Recovery - System Restore</td>
                    <td>Not Set</td>
                    <td>0</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-medium">Medium</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\SystemRestore' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\SystemRestore' -Name 'DisableSR' -Value 0</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>3.1</td>
                    <td>Data Protection - BitLocker</td>
                    <td>Not Set</td>
                    <td>1</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\FVE' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\FVE' -Name 'UseAdvancedStartup' -Value 1</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>12.3</td>
                    <td>Network Infrastructure - Windows Firewall Public</td>
                    <td>Not Set</td>
                    <td>1</td>
                    <td class="status-not-configured">Not Configured</td>
                    <td><span class="risk-high">High</span></td>
                    <td><div class='remediation'>New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PublicProfile' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PublicProfile' -Name 'EnableFirewall' -Value 1</div></td>
                </tr>                <tr>
                    <td>PRD</td>
                    <td>4.1</td>
                    <td>Secure Configuration - Guest Account</td>
                    <td>True</td>
                    <td>True</td>
                    <td class="status-compliant">Compliant</td>
                    <td><span class="risk-high">High</span></td>
                    <td>N/A</td>
                </tr>            </tbody>
        </table>
    </div>
    
    <div style="margin-top: 20px; padding: 15px; background-color: #e9ecef; border-radius: 5px;">
        <h3>Report Information</h3>
        <p><strong>Framework:</strong> CIS Controls v8</p>
        <p><strong>Target OS:</strong> Windows Server 2019</p>
        <p><strong>Audit Date:</strong> 2025-08-19 13:41:52</p>
        <p><strong>Total Controls Evaluated:</strong> 20</p>
    </div>
</body>
</html>
