Control ID,Risk Level,Finding Description,Root Cause,Associated Risk,Recommendation,Target Date
"CIS-4.2","Critical","Default SA account is enabled providing unrestricted database access. Critical vulnerability allowing complete system compromise through default administrative credentials.","Default SA account not disabled during SQL Server installation and hardening process.","Complete database compromise, unauthorized data access, privilege escalation attacks.","Execute 'ALTER LOGIN sa DISABLE;' immediately to disable the SA account and eliminate critical vulnerability.","2025-08-19"
"CIS-4.1","High","SQL Server configured with Mixed Mode Authentication instead of Windows Authentication Only, increasing attack surface.","Authentication mode not changed from default mixed mode to Windows-only during security hardening.","Credential-based attacks, bypassing Windows security policies, increased authentication attack surface.","Change authentication mode to Windows Only using SQL Server Configuration Manager and restart service.","2025-08-20"
"CIS-3.1","High","Production database AGQPROD is not encrypted with Transparent Data Encryption (TDE), storing sensitive data in plaintext.","TDE not implemented during database deployment, encryption state shows 0 (not encrypted).","Data breach exposure, regulatory compliance violations, unauthorized access to sensitive data at rest.","Implement TDE by creating master key, certificate, and database encryption key for AGQPROD database.","2025-08-19"
"CIS-3.2","High","Force Encryption is disabled allowing network communications to be transmitted in plaintext between clients and server.","SSL/TLS encryption not enforced in SQL Server Network Configuration, force encryption setting disabled.","Man-in-the-middle attacks, credential interception, sensitive data exposure during network transmission.","Enable Force Encryption in SQL Server Configuration Manager and install valid SSL certificate.","2025-08-20"
"CIS-8.1","High","No SQL Server audits are configured or enabled, providing zero security event logging and monitoring capabilities.","Audit specifications not created during initial setup, no server audit objects configured.","No forensic capabilities, compliance violations, inability to detect security incidents or unauthorized access.","Create server audit specification with file target and enable auditing for login events and security changes.","2025-08-20"
"CIS-12.3","Medium","Remote access configuration option is enabled (value 1) instead of disabled, providing unnecessary remote connectivity capability.","Remote access sp_configure option not disabled during security hardening, default value maintained.","Remote exploitation vectors, unnecessary attack surface, potential for remote code execution attacks.","Execute 'EXEC sp_configure remote access, 0; RECONFIGURE;' to disable remote access capability.","2025-08-25"
"CIS-4.4","Medium","Password expiration policy not enforced for SA account, allowing passwords to remain unchanged indefinitely.","Password policy settings not configured properly, expiration checking disabled for critical accounts.","Weak password management, credential persistence, increased risk of credential-based attacks.","Execute 'ALTER LOGIN sa WITH CHECK_EXPIRATION = ON;' to enforce password expiration policies.","2025-08-25"
