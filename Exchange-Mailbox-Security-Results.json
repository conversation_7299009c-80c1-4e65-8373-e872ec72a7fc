{"AuditMetadata": {"Error": "The term 'Get-OrganizationConfig' is not recognized as a name of a cmdlet, function, script file, or executable program.\r\nCheck the spelling of the name, or if a path was included, verify that the path is correct and try again."}, "CriticalError": {"StackTrace": null, "Message": "Exchange PowerShell module required", "Timestamp": "2025-08-25T18:10:28.3743794+03:00"}, "AdministratorDiscovery": {"AdminDomainMap": {}, "AdminClassification": {}, "RoleAssignments": [], "CrossDomainRelationships": []}}