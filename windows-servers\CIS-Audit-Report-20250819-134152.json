{"Summary": {"HighRiskIssues": 1, "Errors": 0, "NotConfigured": 11, "MediumRiskIssues": 4, "Compliant": 4, "TotalControls": 20, "CompliancePercentage": 20.0, "NonCompliant": 5}, "AuditMetadata": {"Framework": "CIS Controls v8", "TotalSystems": 1, "TargetOS": "Windows Server 2019", "GeneratedDate": "2025-08-19 13:41:52"}, "Results": [{"ComputerName": "PRD", "ControlID": "1.2", "ControlName": "Asset Inventory - SSDP Discovery", "Description": "SSDP Discovery service should be disabled", "Type": "Service", "CurrentValue": 3, "RecommendedValue": 4, "ComplianceStatus": "Non-Compliant", "RiskLevel": "Medium", "Remediation": "Set-Service -Name 'SSDPSRV' -StartupType Disabled", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "12.1", "ControlName": "Network Infrastructure - Windows Firewall Domain", "Description": "Windows Firewall Domain Profile should be enabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 1, "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\DomainProfile' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\DomainProfile' -Name 'EnableFirewall' -Value 1", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "5.2", "ControlName": "Account Management - Account Lockout", "Description": "Account lockout threshold should be 5 or fewer invalid attempts", "Type": "SecurityPolicy", "CurrentValue": 10, "RecommendedValue": 5, "ComplianceStatus": "Non-Compliant", "RiskLevel": "Medium", "Remediation": "Configure via Group Policy: Computer Configuration > Policies > Windows Settings > Security Settings > Account Policies > Account Lockout Policy", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "10.2", "ControlName": "Malware Defenses - Real-time Protection", "Description": "Windows Defender real-time protection should be enabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 0, "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection' -Name 'DisableRealtimeMonitoring' -Value 0", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "9.1", "ControlName": "Email/Web Protection - SmartScreen", "Description": "Windows Defender SmartScreen should be enabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 1, "ComplianceStatus": "Not Configured", "RiskLevel": "Medium", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\System' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\System' -Name 'EnableSmartScreen' -Value 1", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "4.2", "ControlName": "Secure Configuration - Anonymous SID Enumeration", "Description": "Do not allow anonymous enumeration of SAM accounts", "Type": "Registry", "CurrentValue": 1, "RecommendedValue": 1, "ComplianceStatus": "Compliant", "RiskLevel": "Medium", "Remediation": "", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "1.1", "ControlName": "Asset Inventory - Network Discovery", "Description": "Function Discovery Resource Publication service should be disabled", "Type": "Service", "CurrentValue": 3, "RecommendedValue": 4, "ComplianceStatus": "Non-Compliant", "RiskLevel": "Medium", "Remediation": "Set-Service -Name 'FDResPub' -StartupType Disabled", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "16.1", "ControlName": "Application Security - PowerShell Execution Policy", "Description": "PowerShell execution policy should be RemoteSigned or more restrictive", "Type": "Registry", "CurrentValue": "Restricted", "RecommendedValue": "RemoteSigned", "ComplianceStatus": "Non-Compliant", "RiskLevel": "Medium", "Remediation": "Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell' -Name 'ExecutionPolicy' -Value RemoteSigned", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "10.1", "ControlName": "Malware Defenses - Windows Defender", "Description": "Windows Defender Antivirus should be enabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 0, "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows Defender' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows Defender' -Name 'DisableAntiSpyware' -Value 0", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "5.1", "ControlName": "Account Management - Password Policy", "Description": "Minimum password length should be 14 characters", "Type": "SecurityPolicy", "CurrentValue": 0, "RecommendedValue": 14, "ComplianceStatus": "Non-Compliant", "RiskLevel": "High", "Remediation": "Configure via Group Policy: Computer Configuration > Policies > Windows Settings > Security Settings > Account Policies > Password Policy", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "7.1", "ControlName": "Vulnerability Management - Windows Update", "Description": "Configure Automatic Updates should be enabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 0, "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU' -Name 'NoAutoUpdate' -Value 0", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "8.1", "ControlName": "Audit Log Management - Success Logon", "Description": "Audit logon events should be configured for Success", "Type": "AuditPolicy", "CurrentValue": "Success and Failure", "RecommendedValue": "Success", "ComplianceStatus": "Compliant", "RiskLevel": "Medium", "Remediation": "", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "8.2", "ControlName": "Audit Log Management - Failure Logon", "Description": "Audit logon events should be configured for Failure", "Type": "AuditPolicy", "CurrentValue": "Success and Failure", "RecommendedValue": "Failure", "ComplianceStatus": "Compliant", "RiskLevel": "Medium", "Remediation": "", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "2.1", "ControlName": "Software Inventory - Windows Installer", "Description": "Always install with elevated privileges should be disabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 0, "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer' -Name 'AlwaysInstallElevated' -Value 0", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "6.1", "ControlName": "Access Control - User Rights Assignment", "Description": "Access this computer from the network user right assignment", "Type": "SecurityPolicy", "CurrentValue": null, "RecommendedValue": ["Administrators", "Authenticated Users"], "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "12.2", "ControlName": "Network Infrastructure - Windows Firewall Private", "Description": "Windows Firewall Private Profile should be enabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 1, "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\PrivateProfile' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\PrivateProfile' -Name 'EnableFirewall' -Value 1", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "11.1", "ControlName": "Data Recovery - System Restore", "Description": "System Restore should be enabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 0, "ComplianceStatus": "Not Configured", "RiskLevel": "Medium", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\SystemRestore' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\SystemRestore' -Name 'DisableSR' -Value 0", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "3.1", "ControlName": "Data Protection - BitLocker", "Description": "BitLocker should require additional authentication at startup", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 1, "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\FVE' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\FVE' -Name 'UseAdvancedStartup' -Value 1", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "12.3", "ControlName": "Network Infrastructure - Windows Firewall Public", "Description": "Windows Firewall Public Profile should be enabled", "Type": "Registry", "CurrentValue": null, "RecommendedValue": 1, "ComplianceStatus": "Not Configured", "RiskLevel": "High", "Remediation": "New-Item -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\PublicProfile' -Force; Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\PublicProfile' -Name 'EnableFirewall' -Value 1", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}, {"ComputerName": "PRD", "ControlID": "4.1", "ControlName": "Secure Configuration - Guest Account", "Description": "Guest account should be disabled", "Type": "SecurityPolicy", "CurrentValue": true, "RecommendedValue": true, "ComplianceStatus": "Compliant", "RiskLevel": "High", "Remediation": "", "AuditDate": "2025-08-19T13:41:51.8031651+03:00"}]}